{"name": "pearleseed-portfolio", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "bun run --bun vite", "build": "bun run --bun vite build", "build:dev": "bun run --bun vite build --mode development", "build:analyze": "bun run --bun vite build --mode analyze", "lint": "bun run --bun eslint .", "preview": "bun run --bun vite preview", "perf": "bun run build && bun run preview --host"}, "dependencies": {"@codemirror/autocomplete": "6.18.6", "@codemirror/lang-css": "6.3.1", "@codemirror/lang-html": "6.4.9", "@codemirror/lang-javascript": "6.2.4", "@codemirror/lang-json": "6.0.1", "@codemirror/lang-markdown": "6.3.2", "@codemirror/lang-python": "6.2.1", "@codemirror/language": "6.11.0", "@codemirror/lint": "6.8.5", "@codemirror/state": "6.5.2", "@codemirror/theme-one-dark": "6.1.2", "@codemirror/view": "6.36.8", "@hookform/resolvers": "3.10.0", "@radix-ui/react-accordion": "1.2.10", "@radix-ui/react-alert-dialog": "1.1.13", "@radix-ui/react-aspect-ratio": "1.1.6", "@radix-ui/react-avatar": "1.1.9", "@radix-ui/react-checkbox": "1.3.1", "@radix-ui/react-collapsible": "1.1.10", "@radix-ui/react-context-menu": "2.2.14", "@radix-ui/react-dialog": "1.1.13", "@radix-ui/react-dropdown-menu": "2.1.14", "@radix-ui/react-hover-card": "1.1.13", "@radix-ui/react-label": "2.1.6", "@radix-ui/react-menubar": "1.1.14", "@radix-ui/react-navigation-menu": "1.2.12", "@radix-ui/react-popover": "1.1.13", "@radix-ui/react-progress": "1.1.6", "@radix-ui/react-radio-group": "1.3.6", "@radix-ui/react-scroll-area": "1.2.8", "@radix-ui/react-select": "2.2.4", "@radix-ui/react-separator": "1.1.6", "@radix-ui/react-slider": "1.3.4", "@radix-ui/react-slot": "1.2.2", "@radix-ui/react-switch": "1.2.4", "@radix-ui/react-tabs": "1.1.11", "@radix-ui/react-toast": "1.2.13", "@radix-ui/react-toggle": "1.1.8", "@radix-ui/react-toggle-group": "1.1.9", "@radix-ui/react-tooltip": "1.2.6", "@tabler/icons-react": "3.31.0", "@tanstack/react-query": "5.75.7", "@types/react-beautiful-dnd": "13.1.8", "@types/react-syntax-highlighter": "15.5.13", "caniuse-lite": "1.0.30001717", "canvas-confetti": "1.9.3", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "1.1.1", "codemirror": "6.0.1", "date-fns": "4.1.0", "embla-carousel-react": "8.6.0", "framer-motion": "12.10.5", "highlight.js": "11.11.1", "input-otp": "1.4.2", "lucide-react": "0.462.0", "next-themes": "0.3.0", "qrcode.react": "4.2.0", "react": "18.3.1", "react-beautiful-dnd": "13.1.1", "react-day-picker": "8.10.1", "react-dom": "18.3.1", "react-hook-form": "7.56.3", "react-intersection-observer": "9.16.0", "react-markdown": "10.1.0", "react-resizable-panels": "2.1.9", "react-router-dom": "6.30.0", "react-scroll-parallax": "3.4.5", "react-syntax-highlighter": "15.6.1", "react-type-animation": "3.2.0", "recharts": "2.15.3", "rehype-autolink-headings": "7.1.0", "rehype-highlight": "7.0.2", "rehype-slug": "6.0.0", "remark-gfm": "4.0.1", "sonner": "1.7.4", "tailwind-merge": "2.6.0", "tailwindcss-animate": "1.0.7", "vaul": "0.9.9", "zod": "3.24.4"}, "devDependencies": {"@eslint/js": "9.26.0", "@tailwindcss/typography": "0.5.16", "@types/node": "22.15.17", "@types/react": "18.3.21", "@types/react-dom": "18.3.7", "@vitejs/plugin-react-swc": "3.9.0", "autoprefixer": "10.4.21", "bun-types": "1.2.13", "eslint": "9.26.0", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "0.4.20", "globals": "15.15.0", "lovable-tagger": "1.1.8", "postcss": "8.5.3", "rollup-plugin-visualizer": "5.14.0", "tailwindcss": "3.4.17", "typescript": "5.8.3", "typescript-eslint": "8.32.0", "vite": "5.4.19"}}