# Background Components Documentation

This document describes the enhanced background components system that provides impressive visual backgrounds while maintaining optimal performance across all pages.

## Overview

The background system consists of three main components:

1. **SectionBackground** - Enhanced section-specific backgrounds with new variants
2. **PageBackground** - Full-page backgrounds with parallax effects
3. **AnimatedBackground** - Dynamic animated backgrounds with various patterns

## Components

### SectionBackground

Enhanced version of the existing component with new variants and improved performance.

#### Usage

```tsx
import SectionBackground from '@/components/utils/SectionBackground';

<SectionBackground 
  sectionId="unique-section-id"
  variant="blog" 
  optimizeRendering={true}
  intensity="medium"
  theme="default"
>
  <section>Your content here</section>
</SectionBackground>
```

#### Props

- `sectionId: string` - Unique identifier for the section
- `variant?: string` - Background variant (see variants below)
- `optimizeRendering?: boolean` - Enable performance optimizations
- `intensity?: 'subtle' | 'medium' | 'strong'` - Background intensity
- `theme?: 'default' | 'tech' | 'creative' | 'professional' | 'playful'` - Background theme

#### Available Variants

- `hero` - Hero section background
- `services` - Services section background
- `projects` - Projects section background
- `experience` - Experience section background
- `certifications` - Certifications section background
- `contact` - Contact section background
- `blog` - Blog pages background (NEW)
- `resources` - Resources pages background (NEW)
- `playground` - Playground pages background (NEW)
- `shortener` - URL shortener background (NEW)
- `form` - Form pages background (NEW)
- `error` - Error pages background (NEW)
- `dashboard` - Dashboard background (NEW)

### PageBackground

Full-page background component with parallax effects and performance optimizations.

#### Usage

```tsx
import PageBackground from '@/components/utils/PageBackground';

<PageBackground 
  variant="blog"
  backgroundImage="/path/to/image.jpg"
  overlayOpacity={0.1}
  enableParallax={true}
  className="min-h-screen"
>
  <div>Your page content here</div>
</PageBackground>
```

#### Props

- `variant?: string` - Background color scheme variant
- `backgroundImage?: string` - Optional background image URL
- `overlayOpacity?: number` - Opacity of overlay on background image (0-1)
- `enableParallax?: boolean` - Enable parallax scrolling effects
- `className?: string` - Additional CSS classes

#### Features

- **Parallax Effects**: Smooth parallax scrolling on background images
- **Performance Detection**: Automatically disables effects on low-performance devices
- **Reduced Motion Support**: Respects user's motion preferences
- **Intersection Observer**: Only animates when in view
- **Responsive**: Adapts to different screen sizes

### AnimatedBackground

Dynamic animated backgrounds with various animation patterns.

#### Usage

```tsx
import AnimatedBackground from '@/components/utils/AnimatedBackground';

<AnimatedBackground 
  variant="particles"
  intensity="medium"
  colorScheme="blue"
  enableScrollEffects={true}
>
  <div>Your content here</div>
</AnimatedBackground>
```

#### Props

- `variant?: 'particles' | 'waves' | 'geometric' | 'gradient' | 'minimal'` - Animation pattern
- `intensity?: 'subtle' | 'medium' | 'strong'` - Animation intensity
- `colorScheme?: string` - Color scheme (blue, teal, purple, green, orange, pink, gray)
- `enableScrollEffects?: boolean` - Enable scroll-based effects
- `className?: string` - Additional CSS classes

#### Animation Variants

- **particles** - Floating particle animation
- **waves** - Wave-like flowing animations
- **geometric** - Rotating geometric shapes
- **gradient** - Animated gradient backgrounds
- **minimal** - Subtle static gradients

## Performance Optimizations

### Automatic Performance Detection

All components include built-in performance detection:

```tsx
const { isLowPerformanceDevice, prefersReducedMotion } = usePerformanceDetection();
```

### Optimizations Applied

1. **Device Performance Detection**
   - Disables complex animations on low-performance devices
   - Provides static fallbacks for better performance

2. **Reduced Motion Support**
   - Respects `prefers-reduced-motion` CSS media query
   - Provides non-animated alternatives

3. **Intersection Observer**
   - Only renders animations when components are in view
   - Reduces CPU usage for off-screen elements

4. **Lazy Loading**
   - Background images are loaded lazily
   - Uses `OptimizedImage` component for best performance

5. **CSS Optimizations**
   - Uses `transform` and `opacity` for animations (GPU-accelerated)
   - Minimizes layout thrashing
   - Efficient gradient rendering

## Implementation Examples

### Blog Page

```tsx
// Hero section with enhanced background
<SectionBackground sectionId="blog-hero" variant="blog" optimizeRendering={true}>
  <section className="relative py-24 overflow-hidden">
    {/* Hero content */}
  </section>
</SectionBackground>
```

### Playground Page

```tsx
// Full page background with parallax
<PageBackground variant="playground" enableParallax={true} className="min-h-screen">
  <main className="container py-6 pt-24 relative z-10">
    {/* Page content */}
  </main>
</PageBackground>
```

### Dynamic Content Areas

```tsx
// Animated background for interactive sections
<AnimatedBackground 
  variant="particles" 
  intensity="subtle" 
  colorScheme="teal"
>
  <div className="interactive-content">
    {/* Interactive elements */}
  </div>
</AnimatedBackground>
```

## Migration Guide

### From EnhancedBackground

Replace existing `EnhancedBackground` usage:

```tsx
// Before
<EnhancedBackground optimizeForLowPerformance={true} />

// After - for section backgrounds
<SectionBackground sectionId="unique-id" variant="appropriate-variant" optimizeRendering={true}>
  <section>Content</section>
</SectionBackground>

// After - for full page backgrounds
<PageBackground variant="appropriate-variant" className="min-h-screen">
  <div>Content</div>
</PageBackground>
```

### Choosing the Right Component

- **SectionBackground**: For specific page sections with themed backgrounds
- **PageBackground**: For full-page backgrounds with optional images and parallax
- **AnimatedBackground**: For dynamic, interactive content areas

## Best Practices

1. **Performance First**
   - Always set `optimizeRendering={true}` for SectionBackground
   - Use appropriate intensity levels
   - Test on low-performance devices

2. **Accessibility**
   - Components automatically respect reduced motion preferences
   - Ensure sufficient contrast for text over backgrounds
   - Provide fallbacks for essential content

3. **Visual Consistency**
   - Use consistent variants across similar page types
   - Match color schemes to your design system
   - Consider the overall user experience flow

4. **Resource Management**
   - Optimize background images before use
   - Use appropriate image formats (WebP, AVIF when possible)
   - Consider lazy loading for non-critical backgrounds

## Browser Support

- **Modern Browsers**: Full feature support including animations and parallax
- **Older Browsers**: Graceful degradation to static backgrounds
- **Mobile Devices**: Optimized performance with reduced animations when needed
- **Low-Performance Devices**: Automatic fallback to static, lightweight backgrounds
