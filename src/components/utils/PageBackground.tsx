import React, { useRef, useEffect, useState } from 'react';
import { motion, useScroll, useTransform, useSpring } from 'framer-motion';
import { usePerformanceDetection } from '@/lib/performance-hooks';
import { OptimizedImage } from '@/components/ui/optimized-image';

interface PageBackgroundProps {
  variant?: 'blog' | 'projects' | 'resources' | 'playground' | 'shortener' | 'form' | 'error' | 'dashboard';
  children?: React.ReactNode;
  backgroundImage?: string;
  overlayOpacity?: number;
  enableParallax?: boolean;
  className?: string;
}

/**
 * PageBackground component for full-page backgrounds with parallax effects
 * Optimized for performance with device detection and reduced motion support
 */
const PageBackground: React.FC<PageBackgroundProps> = ({
  variant = 'blog',
  children,
  backgroundImage,
  overlayOpacity = 0.1,
  enableParallax = true,
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isInView, setIsInView] = useState(false);
  const { isLowPerformanceDevice, prefersReducedMotion } = usePerformanceDetection();

  // Scroll-based parallax effect
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  const smoothProgress = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001
  });

  const y = useTransform(smoothProgress, [0, 1], [0, -50]);
  const opacity = useTransform(smoothProgress, [0, 0.2, 0.8, 1], [0.3, 1, 1, 0.3]);

  // Intersection observer for performance optimization
  useEffect(() => {
    if (!containerRef.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => setIsInView(entry.isIntersecting),
      { rootMargin: '100px 0px', threshold: 0.1 }
    );

    observer.observe(containerRef.current);
    return () => observer.disconnect();
  }, []);

  // Get background colors based on variant
  const getBackgroundGradients = () => {
    switch (variant) {
      case 'blog':
        return {
          primary: 'from-indigo-50/80 via-purple-50/60 to-pink-50/80',
          secondary: 'rgba(99, 102, 241, 0.4), rgba(147, 51, 234, 0.4)',
          accent: 'rgba(34, 211, 238, 0.3), rgba(59, 130, 246, 0.3)'
        };
      case 'projects':
        return {
          primary: 'from-teal-50/80 via-blue-50/60 to-cyan-50/80',
          secondary: 'rgba(16, 185, 129, 0.4), rgba(20, 184, 166, 0.4)',
          accent: 'rgba(59, 130, 246, 0.3), rgba(34, 211, 238, 0.3)'
        };
      case 'resources':
        return {
          primary: 'from-emerald-50/80 via-teal-50/60 to-blue-50/80',
          secondary: 'rgba(34, 197, 94, 0.4), rgba(16, 185, 129, 0.4)',
          accent: 'rgba(20, 184, 166, 0.3), rgba(59, 130, 246, 0.3)'
        };
      case 'playground':
        return {
          primary: 'from-cyan-50/80 via-blue-50/60 to-indigo-50/80',
          secondary: 'rgba(59, 130, 246, 0.4), rgba(34, 211, 238, 0.4)',
          accent: 'rgba(34, 197, 94, 0.3), rgba(16, 185, 129, 0.3)'
        };
      case 'shortener':
        return {
          primary: 'from-blue-50/80 via-cyan-50/60 to-teal-50/80',
          secondary: 'rgba(34, 211, 238, 0.4), rgba(59, 130, 246, 0.4)',
          accent: 'rgba(20, 184, 166, 0.3), rgba(34, 197, 94, 0.3)'
        };
      case 'form':
        return {
          primary: 'from-rose-50/80 via-pink-50/60 to-purple-50/80',
          secondary: 'rgba(244, 63, 94, 0.4), rgba(236, 72, 153, 0.4)',
          accent: 'rgba(147, 51, 234, 0.3), rgba(139, 92, 246, 0.3)'
        };
      case 'error':
        return {
          primary: 'from-red-50/80 via-orange-50/60 to-yellow-50/80',
          secondary: 'rgba(249, 115, 22, 0.4), rgba(239, 68, 68, 0.4)',
          accent: 'rgba(234, 179, 8, 0.3), rgba(249, 115, 22, 0.3)'
        };
      case 'dashboard':
        return {
          primary: 'from-slate-50/80 via-gray-50/60 to-blue-50/80',
          secondary: 'rgba(107, 114, 128, 0.4), rgba(100, 116, 139, 0.4)',
          accent: 'rgba(59, 130, 246, 0.3), rgba(34, 211, 238, 0.3)'
        };
      default:
        return {
          primary: 'from-gray-50/80 via-slate-50/60 to-blue-50/80',
          secondary: 'rgba(100, 116, 139, 0.4), rgba(107, 114, 128, 0.4)',
          accent: 'rgba(59, 130, 246, 0.3), rgba(100, 116, 139, 0.3)'
        };
    }
  };

  const gradients = getBackgroundGradients();

  return (
    <div ref={containerRef} className={`relative min-h-screen overflow-hidden ${className}`}>
      {/* Background Image Layer */}
      {backgroundImage && (
        <div className="absolute inset-0 -z-20">
          <OptimizedImage
            src={backgroundImage}
            alt="Background"
            className="w-full h-full object-cover"
            priority={false}
            loadingStrategy="lazy"
            style={{
              transform: enableParallax && !isLowPerformanceDevice && !prefersReducedMotion
                ? `translateY(${y}px)`
                : 'none'
            }}
          />
          <div
            className="absolute inset-0 bg-white"
            style={{ opacity: overlayOpacity }}
          />
        </div>
      )}

      {/* Gradient Background Layers */}
      <div className="absolute inset-0 -z-10">
        {/* Primary gradient */}
        <div className={`absolute inset-0 bg-gradient-to-br ${gradients.primary}`} />

        {/* Animated secondary gradients - only if performance allows */}
        {isInView && !isLowPerformanceDevice && !prefersReducedMotion && (
          <>
            <motion.div
              className="absolute inset-0 opacity-60"
              style={{
                opacity,
                background: `radial-gradient(circle at 20% 30%, ${gradients.secondary})`
              }}
              animate={{
                background: [
                  `radial-gradient(circle at 20% 30%, ${gradients.secondary})`,
                  `radial-gradient(circle at 80% 70%, ${gradients.secondary})`,
                  `radial-gradient(circle at 20% 30%, ${gradients.secondary})`
                ]
              }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            />
            <motion.div
              className="absolute inset-0 opacity-40"
              style={{
                opacity,
                background: `radial-gradient(circle at 70% 20%, ${gradients.accent})`
              }}
              animate={{
                background: [
                  `radial-gradient(circle at 70% 20%, ${gradients.accent})`,
                  `radial-gradient(circle at 30% 80%, ${gradients.accent})`,
                  `radial-gradient(circle at 70% 20%, ${gradients.accent})`
                ]
              }}
              transition={{ duration: 25, repeat: Infinity, ease: "linear", delay: 5 }}
            />
          </>
        )}

        {/* Static fallback for low-performance devices */}
        {(isLowPerformanceDevice || prefersReducedMotion) && (
          <>
            <div
              className="absolute inset-0 opacity-60"
              style={{ background: `radial-gradient(circle at 50% 50%, ${gradients.secondary})` }}
            />
            <div
              className="absolute inset-0 opacity-40"
              style={{ background: `radial-gradient(circle at 50% 50%, ${gradients.accent})` }}
            />
          </>
        )}
      </div>

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};

export default PageBackground;
