import React, { useRef, useEffect, useState } from 'react';
import { motion, useScroll, useTransform, useSpring } from 'framer-motion';
import { usePerformanceDetection } from '@/lib/performance-hooks';

interface AnimatedBackgroundProps {
  variant?: 'particles' | 'waves' | 'geometric' | 'gradient' | 'minimal';
  intensity?: 'subtle' | 'medium' | 'strong';
  colorScheme?: 'blue' | 'teal' | 'purple' | 'green' | 'orange' | 'pink' | 'gray';
  children?: React.ReactNode;
  className?: string;
  enableScrollEffects?: boolean;
}

/**
 * AnimatedBackground component with various animation patterns
 * Optimized for performance with device detection and reduced motion support
 */
const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({
  variant = 'gradient',
  intensity = 'medium',
  colorScheme = 'blue',
  children,
  className = '',
  enableScrollEffects = true
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isInView, setIsInView] = useState(false);
  const { isLowPerformanceDevice, prefersReducedMotion } = usePerformanceDetection();

  // Scroll-based effects
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  const smoothProgress = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001
  });

  const y = useTransform(smoothProgress, [0, 1], [0, -100]);
  const rotate = useTransform(smoothProgress, [0, 1], [0, 360]);
  const scale = useTransform(smoothProgress, [0, 0.5, 1], [0.8, 1.1, 0.8]);

  // Intersection observer for performance optimization
  useEffect(() => {
    if (!containerRef.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => setIsInView(entry.isIntersecting),
      { rootMargin: '100px 0px', threshold: 0.1 }
    );

    observer.observe(containerRef.current);
    return () => observer.disconnect();
  }, []);

  // Get color scheme values
  const getColorScheme = () => {
    const schemes = {
      blue: {
        primary: 'from-blue-400/20 to-cyan-400/20',
        secondary: 'from-indigo-300/15 to-blue-300/15',
        accent: 'from-cyan-200/10 to-blue-200/10'
      },
      teal: {
        primary: 'from-teal-400/20 to-emerald-400/20',
        secondary: 'from-cyan-300/15 to-teal-300/15',
        accent: 'from-emerald-200/10 to-teal-200/10'
      },
      purple: {
        primary: 'from-purple-400/20 to-pink-400/20',
        secondary: 'from-indigo-300/15 to-purple-300/15',
        accent: 'from-pink-200/10 to-purple-200/10'
      },
      green: {
        primary: 'from-green-400/20 to-emerald-400/20',
        secondary: 'from-lime-300/15 to-green-300/15',
        accent: 'from-emerald-200/10 to-green-200/10'
      },
      orange: {
        primary: 'from-orange-400/20 to-red-400/20',
        secondary: 'from-yellow-300/15 to-orange-300/15',
        accent: 'from-red-200/10 to-orange-200/10'
      },
      pink: {
        primary: 'from-pink-400/20 to-rose-400/20',
        secondary: 'from-purple-300/15 to-pink-300/15',
        accent: 'from-rose-200/10 to-pink-200/10'
      },
      gray: {
        primary: 'from-gray-400/20 to-slate-400/20',
        secondary: 'from-zinc-300/15 to-gray-300/15',
        accent: 'from-slate-200/10 to-gray-200/10'
      }
    };
    return schemes[colorScheme] || schemes.blue;
  };

  const colors = getColorScheme();

  // Get intensity multiplier
  const getIntensityMultiplier = () => {
    switch (intensity) {
      case 'subtle': return 0.5;
      case 'strong': return 1.5;
      default: return 1;
    }
  };

  const intensityMultiplier = getIntensityMultiplier();

  // Render background elements based on variant
  const renderBackgroundElements = () => {
    if (isLowPerformanceDevice || prefersReducedMotion) {
      // Static fallback for low-performance devices
      return (
        <div className="absolute inset-0">
          <div className={`absolute inset-0 bg-gradient-to-br ${colors.primary}`} style={{ opacity: 0.3 * intensityMultiplier }} />
          <div className={`absolute inset-0 bg-gradient-to-tl ${colors.secondary}`} style={{ opacity: 0.2 * intensityMultiplier }} />
        </div>
      );
    }

    switch (variant) {
      case 'particles':
        return (
          <div className="absolute inset-0">
            {Array.from({ length: 20 }).map((_, i) => (
              <motion.div
                key={i}
                className={`absolute w-2 h-2 rounded-full bg-gradient-to-r ${colors.accent}`}
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  y: [0, -30, 0],
                  x: [0, Math.random() * 20 - 10, 0],
                  opacity: [0.3, 0.8, 0.3],
                }}
                transition={{
                  duration: 3 + Math.random() * 2,
                  repeat: Infinity,
                  delay: Math.random() * 2,
                  ease: "easeInOut"
                }}
              />
            ))}
          </div>
        );

      case 'waves':
        return (
          <div className="absolute inset-0">
            <motion.div
              className={`absolute inset-0 bg-gradient-to-r ${colors.primary}`}
              style={{ 
                clipPath: 'polygon(0 0, 100% 0, 100% 85%, 0 100%)',
                opacity: 0.4 * intensityMultiplier
              }}
              animate={{
                clipPath: [
                  'polygon(0 0, 100% 0, 100% 85%, 0 100%)',
                  'polygon(0 0, 100% 0, 100% 90%, 0 95%)',
                  'polygon(0 0, 100% 0, 100% 85%, 0 100%)'
                ]
              }}
              transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
            />
            <motion.div
              className={`absolute inset-0 bg-gradient-to-l ${colors.secondary}`}
              style={{ 
                clipPath: 'polygon(0 15%, 100% 0, 100% 100%, 0 100%)',
                opacity: 0.3 * intensityMultiplier
              }}
              animate={{
                clipPath: [
                  'polygon(0 15%, 100% 0, 100% 100%, 0 100%)',
                  'polygon(0 10%, 100% 5%, 100% 100%, 0 100%)',
                  'polygon(0 15%, 100% 0, 100% 100%, 0 100%)'
                ]
              }}
              transition={{ duration: 10, repeat: Infinity, ease: "easeInOut", delay: 2 }}
            />
          </div>
        );

      case 'geometric':
        return (
          <div className="absolute inset-0">
            {Array.from({ length: 8 }).map((_, i) => (
              <motion.div
                key={i}
                className={`absolute bg-gradient-to-br ${colors.primary} rounded-lg`}
                style={{
                  width: `${60 + Math.random() * 40}px`,
                  height: `${60 + Math.random() * 40}px`,
                  left: `${Math.random() * 90}%`,
                  top: `${Math.random() * 90}%`,
                  opacity: 0.1 * intensityMultiplier
                }}
                animate={{
                  rotate: [0, 360],
                  scale: [1, 1.2, 1],
                }}
                transition={{
                  duration: 15 + Math.random() * 10,
                  repeat: Infinity,
                  ease: "linear",
                  delay: Math.random() * 5
                }}
              />
            ))}
          </div>
        );

      case 'gradient':
      default:
        return (
          <div className="absolute inset-0">
            <motion.div
              className={`absolute inset-0 bg-gradient-to-br ${colors.primary}`}
              style={{ opacity: 0.4 * intensityMultiplier }}
              animate={{
                background: [
                  `linear-gradient(45deg, var(--tw-gradient-stops))`,
                  `linear-gradient(135deg, var(--tw-gradient-stops))`,
                  `linear-gradient(45deg, var(--tw-gradient-stops))`
                ]
              }}
              transition={{ duration: 12, repeat: Infinity, ease: "easeInOut" }}
            />
            <motion.div
              className={`absolute inset-0 bg-gradient-to-tl ${colors.secondary}`}
              style={{ opacity: 0.3 * intensityMultiplier }}
              animate={{
                background: [
                  `linear-gradient(225deg, var(--tw-gradient-stops))`,
                  `linear-gradient(315deg, var(--tw-gradient-stops))`,
                  `linear-gradient(225deg, var(--tw-gradient-stops))`
                ]
              }}
              transition={{ duration: 15, repeat: Infinity, ease: "easeInOut", delay: 3 }}
            />
          </div>
        );

      case 'minimal':
        return (
          <div className="absolute inset-0">
            <div className={`absolute inset-0 bg-gradient-to-br ${colors.accent}`} style={{ opacity: 0.2 * intensityMultiplier }} />
          </div>
        );
    }
  };

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        {isInView && renderBackgroundElements()}
      </div>

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};

export default AnimatedBackground;
